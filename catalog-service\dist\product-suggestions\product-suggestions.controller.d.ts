import { ProductSuggestionsService } from './product-suggestions.service';
import { CreateProductSuggestionDto } from './dto/create-product-suggestion.dto';
import { UpdateProductSuggestionDto } from './dto/update-product-suggestion.dto';
export declare class ProductSuggestionsController {
    private readonly productSuggestionsService;
    constructor(productSuggestionsService: ProductSuggestionsService);
    create(createProductSuggestionDto: CreateProductSuggestionDto): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    findAll(merchantId?: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }[]>;
    getPendingSuggestions(): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    update(id: string, updateProductSuggestionDto: UpdateProductSuggestionDto): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    rejectSuggestion(id: string, rejectionReason: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
}
