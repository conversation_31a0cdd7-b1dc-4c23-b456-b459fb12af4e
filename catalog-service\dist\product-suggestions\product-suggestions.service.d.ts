import { PrismaService } from '../prisma/prisma.service';
import { CreateProductSuggestionDto } from './dto/create-product-suggestion.dto';
import { UpdateProductSuggestionDto } from './dto/update-product-suggestion.dto';
import { GlobalProductsService } from '../global-products/global-products.service';
export declare class ProductSuggestionsService {
    private prisma;
    private globalProductsService;
    constructor(prisma: PrismaService, globalProductsService: GlobalProductsService);
    create(createProductSuggestionDto: CreateProductSuggestionDto): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    findAll(): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }[]>;
    findByMerchant(merchantId: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    update(id: string, updateProductSuggestionDto: UpdateProductSuggestionDto): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
    private approveSuggestion;
    getPendingSuggestions(): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }[]>;
    rejectSuggestion(id: string, rejectionReason: string): Promise<{
        id: string;
        description: string | null;
        barcode: string | null;
        brand: string | null;
        category: string;
        imageUrl: string | null;
        standardUnit: string | null;
        merchantShopId: string;
        productName: string;
        status: string;
        adminNotes: string | null;
        submittedAt: Date;
        reviewedAt: Date | null;
        globalProductId: string | null;
    }>;
}
