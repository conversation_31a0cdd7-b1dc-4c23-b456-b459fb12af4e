import { GlobalProductsService } from './global-products.service';
import { CreateGlobalProductDto } from './dto/create-global-product.dto';
import { UpdateGlobalProductDto } from './dto/update-global-product.dto';
export declare class GlobalProductsController {
    private readonly globalProductsService;
    constructor(globalProductsService: GlobalProductsService);
    create(createGlobalProductDto: CreateGlobalProductDto): Promise<{
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findAll(skip?: number, take?: number): Promise<({
        category: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            parentId: string | null;
        } | null;
    } & {
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    search(query: string, categoryId?: string): Promise<({
        category: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            parentId: string | null;
        } | null;
    } & {
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    findOne(id: string): Promise<{
        category: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            parentId: string | null;
        } | null;
    } & {
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    update(id: string, updateGlobalProductDto: UpdateGlobalProductDto): Promise<{
        category: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            parentId: string | null;
        } | null;
    } & {
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: string): Promise<{
        id: string;
        name: string;
        description: string | null;
        sku: string;
        barcode: string | null;
        brand: string | null;
        categoryId: string | null;
        price: number | null;
        currency: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    uploadFile(file: Express.Multer.File): Promise<{
        created: number;
        updated: number;
        failed: number;
        errors: string[];
    }>;
}
